import createMiddleware from "next-intl/middleware";
import { defaultLocale, localePrefix, locales, pathnames } from "./config";
import { NextRequest, NextResponse } from "next/server";

/**
 * 获取用户IP地址
 */
function getClientIP(request: NextRequest): string {
	const forwarded = request.headers.get('x-forwarded-for');
	const realIP = request.headers.get('x-real-ip');
	const cfConnectingIP = request.headers.get('cf-connecting-ip');

	if (forwarded) {
		return forwarded.split(',')[0].trim();
	}

	if (realIP) return realIP;
	if (cfConnectingIP) return cfConnectingIP;

	return request.ip || '127.0.0.1';
}

/**
 * 检查IP是否为中国IP
 */
function isChineseIP(ip: string): boolean {
	// 本地开发环境：切换这个boolean值来测试中国IP行为
	if (ip === '127.0.0.1' || ip === '::1' || ip.startsWith('192.168.') || ip.startsWith('10.') || ip.startsWith('172.')) {
		return true; // 改为 false 可以测试非中国IP行为，改为 true 测试中国IP行为
	}

	const ipParts = ip.split('.').map(Number);
	if (ipParts.length !== 4) return false;

	// 中国主要运营商的IP段首字节
	const chineseFirstOctets = [
		1, 14, 27, 36, 39, 42, 49, 58, 59, 60, 61, 101, 103, 106, 110, 111, 112, 113, 114, 115,
		116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 180, 182, 183, 202, 203, 210, 211,
		218, 219, 220, 221, 222, 223
	];

	return chineseFirstOctets.includes(ipParts[0]);
}

// 定义一个默认导出的中间件函数，用于处理权限验证和路由跳转
export default function middleware(request: NextRequest) {
	const { pathname, searchParams } = request.nextUrl;

	// 检查是否为没有语言前缀的路径
	const hasLocalePrefix = locales.some(locale =>
		pathname === `/${locale}` || pathname.startsWith(`/${locale}/`)
	);

	// 检查是否有语言选择标识（用户主动切换语言时会设置这个参数）
	const hasLanguageSwitch = searchParams.has('lang-switch') || searchParams.get('lang-switch') === '1';

	// 检查是否来自英文页面的重定向（通过Referer头判断）
	const referer = request.headers.get('referer');
	const isFromEnglishPage = referer && (referer.includes('/en/') || referer.includes('/en?'));

	// 检查referer中是否包含语言切换参数
	const isFromLanguageSwitch = referer && referer.includes('lang-switch');

	// 只对根路径进行IP检测重定向，且满足以下条件：
	// 1. 不是语言切换时
	// 2. 不是来自英文页面的重定向
	// 3. referer中不包含语言切换参数
	if (pathname === '/' && !hasLocalePrefix && !hasLanguageSwitch && !isFromEnglishPage && !isFromLanguageSwitch) {
		const clientIP = getClientIP(request);

		// 检测是否为中国IP
		if (isChineseIP(clientIP)) {
			const url = request.nextUrl.clone();
			url.pathname = `/zh-Hans${pathname}`;
			return NextResponse.redirect(url);
		}

		// 非中国IP不做处理，让next-intl处理默认重定向
	}

	// 如果有语言切换标识，清除它并继续处理
	if (hasLanguageSwitch) {
		const url = request.nextUrl.clone();
		url.searchParams.delete('lang-switch');
		// 如果URL发生了变化，重定向到清理后的URL
		if (url.search !== request.nextUrl.search) {
			return NextResponse.redirect(url);
		}
	}

	// 创建下一个中间件实例 多语言中间件
	const next = createMiddleware({
		locales,
		defaultLocale: defaultLocale,
		pathnames: pathnames,
		localePrefix: localePrefix,
		localeDetection: false
	});

	// 其他情况，继续执行下一个中间件
	return next(request);
}


export const config = {
	matcher: [
		// 在根目录中启用重定向到匹配的区域设置
"/",
		// 设置cookie以记住以前的区域设置
		// 所有具有区域设置前缀的请求
		// `/(${locales.map(item => item).join("|")})/:path*`,
		"/(af|am|ar|as|az|ba|bg|bho|bn|bo|brx|bs|ca|cs|cy|da|de|doi|dsb|dv|el|en|es|et|eu|fa|fi|fil|fj|fo|fr|fr-CA|ga|gl|gom|gu|ha|he|hi|hne|hr|hsb|ht|hu|hy|id|ig|ikt|is|it|iu|iu-Latn|ja|ka|kk|km|kmr|kn|ko|ks|ku|ky|ln|lo|lt|lug|lv|lzh|mai|mg|mi|mk|ml|mn-Cyrl|mn-Mong|mni|mr|ms|mt|mww|my|nb|ne|nl|nso|nya|or|otq|pa|pl|prs|ps|pt|pt-PT|ro|ru|run|rw|sd|si|sk|sl|sm|sn|so|sq|sr-Cyrl|sr-Latn|st|sv|sw|ta|te|th|ti|tk|tlh-Latn|tlh-Piqd|tn|to|tr|tt|ty|ug|uk|ur|uz|vi|xh|yo|yua|yue|zh-Hans|zh-Hant|zu)/:path*",
		// 启用添加缺失区域设置的重定向
		//'(e.g. `/pathnames` -> `/en/pathnames`)',
		'/((?!api|_next|_vercel|.*\\..*).*)',
	],
};
